{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc -b", "dev": "pnpm build && node dist/index.js", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10"}}